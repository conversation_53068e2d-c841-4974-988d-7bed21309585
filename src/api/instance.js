import { message } from "antd";
import axios from "axios";
import {
  $getIsIOS,
  $getIsAndroid,
  getUrlQuery,
  getTokenFromCookie,
} from "@/tools";

// // 测试环境
// let baseURL = "https://www.ecscc.net";
// 开发环境
// let baseURL = "http://192.168.38.218";
// let baseURL = "https://ysotjxyt.ysclass.net";
// let baseURL = "http://192.168.38.210";
// let baseURL = "https://www.ecscc.net";
let baseURL = ''
if (process.env.NODE_ENV == "development") {
  baseURL = "https://ysotjxyt.ysclass.net";
} else if (process.env.NODE_ENV == "production") {
  baseURL = "";
}
const instance = axios.create({
  baseURL,
});

instance.interceptors.request.use(
  (config) => {
    if (process.env.NODE_ENV === "production") {
      config = window.ysCommonToolLib.encryption(config);
    }
    if (getUrlQuery("bureauId")) {
      config.headers.bureauId = getUrlQuery("bureauId");
    }
    if (getUrlQuery("jd")) {
      config.headers.jd = getUrlQuery("jd");
    }

    if (config.hiddenToken) {
      return config;
    }
    // http://localhost:3000/#/?bureauId=889487946925871211&jd=0
    config.headers.Authorization = getTokenFromCookie();
    // config.headers.Authorization =
    //   "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oS-lh9nHyCJ5sQfyuTmKRoNM5Eiyc42vOOhMm2nWnnI";
    return config;
  },
  (error) => { }
);
instance.interceptors.response.use(
  (response) => {
    const { status, data, config } = response;
    if (status === 200) {
      if (data.code === 500 && !config.hideMsg && data.msg) {
        message.error(data.msg);
      }
      if (data.code === 401) {
        on401();
      }
      if (data.code === 403) {
        message.error(data.msg);
        return;
      }
      if (data.code === 0) {
        return data;
      }
      if (config.returnWitioutCode) {
        return data;
      }
    }
  },
  (error) => {
    const status = error.response.status;
    if (status === 401) {
      on401();
    }
  }
);

function on401() {
  if (process.env.NODE_ENV === "development") return;
  const backUrl = "?redirect=" + encodeURIComponent(window.location.href);
  if ($getIsAndroid() || $getIsIOS()) {
    return (window.location.href = "/yskt/h5/#/login" + backUrl);
  } else {
    window.location.href = "/zhxy/#/redirect/login" + backUrl;
  }
}

export default instance;
